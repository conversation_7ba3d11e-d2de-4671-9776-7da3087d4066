# TaskGroupSelect 动态搜索功能实现

## 📋 功能概述

为 `TaskGroupSelect` 组件添加了动态搜索功能，支持两种数据加载模式：
1. **初始化加载模式**：组件挂载时自动加载数据（默认行为）
2. **动态搜索模式**：点击下拉框时才加载数据（新增功能）

## 🔧 技术实现

### 1. 组件属性扩展

在 `TaskGroupSelectProps` 接口中新增了 `dynamicSearch` 属性：

```typescript
interface TaskGroupSelectProps {
  // ... 其他属性
  /** 是否启用动态搜索模式（点击时加载数据） */
  dynamicSearch?: boolean;
}
```

### 2. 核心逻辑修改

#### 状态管理
```typescript
const [isDropdownOpen, setIsDropdownOpen] = useState(false);
```

#### 下拉框事件处理
```typescript
const handleOpenChange = useCallback((open: boolean) => {
  setIsDropdownOpen(open);
  
  // 如果是动态搜索模式，且下拉框打开时才加载数据
  if (dynamicSearch && open && options.length === 0) {
    loadTaskGroups();
  }
}, [dynamicSearch, options.length, loadTaskGroups]);
```

#### 条件加载逻辑
```typescript
// 组件挂载时加载数据（仅在非动态搜索模式下）
useEffect(() => {
  if (!dynamicSearch) {
    loadTaskGroups();
  }
}, [loadTaskGroups, dynamicSearch]);
```

### 3. UI 优化

- 动态搜索模式下，未加载数据时显示 "点击加载数据"
- 加载过程中显示 "加载中..." 状态
- 使用新的 `onOpenChange` API 替代已弃用的 `onDropdownVisibleChange`

## 🚀 使用方法

### 基础用法（初始化加载）
```tsx
<TaskGroupSelect 
  placeholder="请选择任务分组"
  allowClear
/>
```

### 动态搜索模式
```tsx
<TaskGroupSelect 
  placeholder="请选择任务分组"
  allowClear
  dynamicSearch={true}  // 启用动态搜索
/>
```

### 在表单中使用
```tsx
<Form.Item name="group" label="任务分组">
  <TaskGroupSelect 
    placeholder="请选择任务分组"
    dynamicSearch={true}
  />
</Form.Item>
```

## 📁 修改的文件

### 核心组件
- `src/components/common/TaskGroupSelect.tsx` - 主要修改
  - 新增 `dynamicSearch` 属性
  - 实现条件加载逻辑
  - 优化用户体验

### 集成更新
- `src/components/AntdTable.tsx` - 启用动态搜索
  - 快速搜索区域的任务分组选择器
  - 详细查询模态框中的任务分组选择器

- `src/components/ComplexTaskForm.tsx` - 组件替换和优化
  - 将普通 Input 替换为 TaskGroupSelect
  - 修复数据类型映射问题
  - 启用动态搜索模式

### 测试和演示
- `src/components/TaskGroupSelectTest.tsx` - 新增测试页面
- `src/router.tsx` - 添加测试路由

## 🎯 功能特点

### 性能优化
- **减少初始请求**：动态搜索模式下，页面加载时不会发起网络请求
- **按需加载**：只有用户真正需要选择时才加载数据
- **缓存机制**：数据加载后会缓存，避免重复请求

### 用户体验
- **加载状态**：清晰的加载提示和状态显示
- **错误处理**：完善的错误提示和处理机制
- **搜索功能**：支持本地搜索过滤

### 兼容性
- **向后兼容**：默认行为保持不变，不影响现有代码
- **灵活配置**：可根据具体场景选择合适的加载模式
- **类型安全**：完整的 TypeScript 类型支持

## 🧪 测试验证

### 访问测试页面
```
http://localhost:5174/task-group-select-test
```

### 测试步骤
1. **对比测试**：观察两种模式的加载时机差异
2. **网络监控**：通过浏览器开发者工具查看网络请求
3. **功能验证**：测试选择、搜索、清除等功能
4. **表单集成**：验证在表单中的使用效果

### 预期结果
- 初始化加载模式：页面加载时立即发起请求
- 动态搜索模式：点击下拉框时才发起请求
- 两种模式的功能表现完全一致

## 📊 适用场景

### 初始化加载模式
- ✅ 数据量较小（< 100 条）
- ✅ 用户经常使用该选择器
- ✅ 网络环境良好
- ✅ 需要快速响应的场景

### 动态搜索模式
- ✅ 数据量较大（> 100 条）
- ✅ 用户不一定会使用该选择器
- ✅ 网络环境较差或移动端
- ✅ 需要优化页面加载速度的场景

## 🔄 后续优化建议

1. **智能缓存**：实现更智能的缓存策略，支持数据过期和刷新
2. **虚拟滚动**：对于大量数据，可以考虑实现虚拟滚动
3. **搜索优化**：支持服务端搜索，而不仅仅是本地过滤
4. **预加载策略**：在用户鼠标悬停时预加载数据
5. **性能监控**：添加性能监控，统计加载时间和用户行为

## 📝 更新日志

### v1.2.0 (当前版本)
- 🔄 **重大更新**：接口调用从 `getTaskGroups(params)` 改为 `getTaskGroupAll()`
- ❌ **移除功能**：删除 `onlyUsed` 和 `onlyUnused` 属性（接口不再需要参数）
- 🧹 **代码清理**：简化组件逻辑，移除不必要的参数处理
- 📝 **文档更新**：更新所有相关文档和示例
- 🧪 **测试更新**：更新单元测试以匹配新的接口调用

### v1.1.0
- ✨ 新增动态搜索模式
- 🐛 修复 API 兼容性问题（使用 onOpenChange 替代 onDropdownVisibleChange）
- 💄 优化用户界面和提示信息
- 🔧 完善 TypeScript 类型定义
- 📝 添加完整的测试页面和文档
