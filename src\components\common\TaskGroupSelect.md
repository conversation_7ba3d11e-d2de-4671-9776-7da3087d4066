# TaskGroupSelect 组件

一个用于选择任务分组的 React 组件，基于 Ant Design 的 Select 组件构建。

## 功能特性

- ✅ 自动从 `TaskService.getTaskGroupAll()` 加载分组数据
- ✅ 支持搜索过滤功能
- ✅ 支持清除选择
- ✅ 支持初始化加载和动态搜索两种模式
- ✅ 支持不同尺寸（small, middle, large）
- ✅ 支持禁用状态
- ✅ 加载状态显示
- ✅ 完善的错误处理
- ✅ TypeScript 类型支持

## 安装和导入

```tsx
import { TaskGroupSelect } from './common/TaskGroupSelect';
```

## 基本用法

### 1. 简单使用

```tsx
import React, { useState } from 'react';
import { TaskGroupSelect } from './common/TaskGroupSelect';

const MyComponent = () => {
  const [selectedGroup, setSelectedGroup] = useState<string>();

  return (
    <TaskGroupSelect 
      value={selectedGroup}
      onChange={setSelectedGroup}
      placeholder="请选择任务分组"
    />
  );
};
```

### 2. 在表单中使用

```tsx
import { Form } from 'antd';
import { TaskGroupSelect } from './common/TaskGroupSelect';

const TaskForm = () => {
  const [form] = Form.useForm();

  return (
    <Form form={form}>
      <Form.Item
        name="taskGroup"
        label="任务分组"
        rules={[{ required: true, message: '请选择任务分组' }]}
      >
        <TaskGroupSelect placeholder="请选择任务分组" />
      </Form.Item>
    </Form>
  );
};
```

### 3. 动态搜索模式

```tsx
// 启用动态搜索（点击时才加载数据）
<TaskGroupSelect
  dynamicSearch={true}
  placeholder="请选择任务分组"
/>

// 默认模式（组件挂载时加载数据）
<TaskGroupSelect
  placeholder="请选择任务分组"
/>
```

### 4. 不同尺寸

```tsx
// 小尺寸
<TaskGroupSelect size="small" />

// 中等尺寸（默认）
<TaskGroupSelect size="middle" />

// 大尺寸
<TaskGroupSelect size="large" />
```

## API 参数

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| value | 当前选中的值 | `string` | - | 否 |
| onChange | 值变化回调 | `(value: string) => void` | - | 否 |
| placeholder | 占位符文本 | `string` | `'请选择任务分组'` | 否 |
| allowClear | 是否允许清除 | `boolean` | `true` | 否 |
| className | 自定义样式类名 | `string` | `'w-full rounded-md'` | 否 |
| style | 自定义样式 | `React.CSSProperties` | - | 否 |
| disabled | 是否禁用 | `boolean` | `false` | 否 |
| showSearch | 是否显示搜索功能 | `boolean` | `true` | 否 |
| size | 选择框大小 | `'small' \| 'middle' \| 'large'` | `'middle'` | 否 |
| dynamicSearch | 是否启用动态搜索模式 | `boolean` | `false` | 否 |

## 数据源

组件通过调用 `TaskService.getTaskGroupAll()` 获取分组数据，返回的数据结构应符合 `TaskBasicGroupFormData` 类型：

```typescript
interface TaskBasicGroupFormData {
  id: number;
  name: string;
  is_used: boolean;
  create_time: string;
  update_time: string;
}
```

组件会自动将 `name` 字段作为选项的标签和值。

## 错误处理

- 当数据加载失败时，会显示错误消息
- 当没有数据时，会显示 "暂无数据"
- 加载过程中会显示 "加载中..." 状态

## 样式定制

组件使用 Tailwind CSS 类名，你可以通过 `className` 和 `style` 属性进行样式定制：

```tsx
<TaskGroupSelect 
  className="w-80 shadow-lg"
  style={{ 
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
  }}
/>
```

## 注意事项

1. 确保项目中已正确配置 `TaskService` 和相关的 API 接口
2. 组件依赖 Ant Design，确保项目中已安装并正确配置
3. 如果使用 TypeScript，确保导入了相关的类型定义
4. 动态搜索模式适合数据量大或不常用的场景，可以减少初始加载时间

## 示例文件

- `src/components/TaskGroupSelectDemo.tsx` - 基础演示
- `src/components/examples/TaskGroupSelectExample.tsx` - 详细示例
- `src/components/examples/TaskFormWithGroupSelect.tsx` - 实际业务场景示例

## 更新日志

### v1.0.0
- 初始版本
- 支持基本的分组选择功能
- 支持搜索和过滤
- 支持不同尺寸和状态
